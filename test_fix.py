#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的首板策略数据生成逻辑
"""

import pandas as pd
import numpy as np
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_shouban_labels():
    """测试首板标签生成逻辑"""
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 1000
    
    test_data = pd.DataFrame({
        'ts_code': [f'00000{i%100:02d}.SZ' for i in range(n_samples)],
        'trade_date': pd.date_range('2025-01-01', periods=n_samples, freq='D'),
        'limit_up': np.random.choice([True, False], n_samples, p=[0.05, 0.95]),
        '连续涨停天数': np.random.choice([0, 1, 2, 3], n_samples, p=[0.8, 0.15, 0.04, 0.01]),
        'pct_chg': np.random.normal(0, 3, n_samples),
        'close': np.random.uniform(10, 100, n_samples),
        'high': np.random.uniform(10, 100, n_samples),
        'future_1_day_open': np.random.uniform(10, 100, n_samples),
        'future_1_day_pct_chg': np.random.normal(0, 4, n_samples),
        'future_1_day_limit_up': np.random.choice([True, False], n_samples, p=[0.03, 0.97]),
        '盘中炸板再涨停': np.random.choice([0, 1], n_samples, p=[0.95, 0.05])
    })
    
    # 确保high >= close
    test_data['high'] = np.maximum(test_data['high'], test_data['close'])
    
    logging.info(f"创建测试数据: {len(test_data)} 条记录")
    
    # 应用修复后的标签逻辑
    def apply_fixed_shouban_labels(df_result):
        """应用修复后的首板标签逻辑"""
        
        # 首板成功：当日首板涨停 + (次日有溢价1%以上 OR 次日涨幅>3%)
        shouban_condition = (
            (df_result['limit_up'] == True) &  # 当日涨停
            (df_result['连续涨停天数'] == 1)     # 首板
        )
        # 放宽条件：降低溢价要求并增加备选条件
        premium_condition = (
            (df_result['future_1_day_open'] > df_result['close'] * 1.01) |  # 次日开盘溢价1%以上
            (df_result['future_1_day_pct_chg'] > 3.0)  # 或次日涨幅>3%
        )
        df_result['shouban_success'] = (shouban_condition & premium_condition).astype(int)

        # 首板失败：首板涨停但次日表现差，或当日炸板
        no_premium_condition = (
            (df_result['future_1_day_open'] <= df_result['close'] * 0.99) &  # 次日开盘低开1%以上
            (df_result['future_1_day_pct_chg'] < -2.0)  # 且次日跌幅>2%
        )
        zhaban_condition = (
            (df_result.get('盘中炸板再涨停', 0) == 0) &  # 没有重新封板
            (df_result['high'] > df_result['close'] * 1.095)  # 盘中曾经接近涨停
        )
        df_result['shouban_failure'] = (
            (shouban_condition & no_premium_condition) | zhaban_condition
        ).astype(int)
        
        return df_result
    
    # 应用标签
    test_data = apply_fixed_shouban_labels(test_data)
    
    # 统计结果
    positive_samples = (test_data['shouban_success'] == 1).sum()
    negative_samples = (test_data['shouban_failure'] == 1).sum()
    neutral_samples = len(test_data) - positive_samples - negative_samples
    
    logging.info(f"标签统计结果:")
    logging.info(f"  正样本(首板成功): {positive_samples}个")
    logging.info(f"  负样本(首板失败): {negative_samples}个")
    logging.info(f"  中性样本: {neutral_samples}个")
    
    if positive_samples > 0 and negative_samples > 0:
        balance_ratio = min(positive_samples, negative_samples) / max(positive_samples, negative_samples)
        logging.info(f"  样本平衡度: {balance_ratio:.2f}")
        
        if balance_ratio >= 0.3:
            logging.info("  ✅ 样本分布合理")
        else:
            logging.warning("  ⚠️ 样本仍然不平衡")
    else:
        logging.error("  ❌ 仍然存在样本为0的问题")
    
    # 测试多层次标签逻辑
    def test_multilevel_labels(current_row):
        """测试多层次标签逻辑"""
        shouban_success = current_row.get('shouban_success', np.nan)
        shouban_failure = current_row.get('shouban_failure', np.nan)
        future_1_day = current_row.get('future_1_day_limit_up', False)
        future_1_day_pct = current_row.get('future_1_day_pct_chg', 0)
        
        # 多层次标签逻辑
        if pd.notna(shouban_success) and shouban_success == 1:
            return 1  # 明确的首板成功
        elif pd.notna(shouban_failure) and shouban_failure == 1:
            return 0  # 明确的首板失败
        elif pd.notna(future_1_day) and future_1_day:
            return 1  # 次日涨停
        elif pd.notna(future_1_day_pct) and future_1_day_pct > 5.0:
            return 1  # 次日大涨>5%
        elif pd.notna(future_1_day_pct) and future_1_day_pct < -3.0:
            return 0  # 次日大跌<-3%
        else:
            current_pct = current_row.get('pct_chg', 0)
            return 1 if current_pct > 7.0 else 0  # 默认基于当日涨幅
    
    # 应用多层次标签
    test_data['multilevel_label'] = test_data.apply(test_multilevel_labels, axis=1)
    
    # 统计多层次标签结果
    multilevel_positive = (test_data['multilevel_label'] == 1).sum()
    multilevel_negative = (test_data['multilevel_label'] == 0).sum()
    
    logging.info(f"多层次标签统计:")
    logging.info(f"  正样本: {multilevel_positive}个")
    logging.info(f"  负样本: {multilevel_negative}个")
    
    if multilevel_positive > 0 and multilevel_negative > 0:
        balance_ratio = min(multilevel_positive, multilevel_negative) / max(multilevel_positive, multilevel_negative)
        logging.info(f"  样本平衡度: {balance_ratio:.2f}")
        
        if balance_ratio >= 0.2:
            logging.info("  ✅ 多层次标签逻辑有效")
            return True
        else:
            logging.warning("  ⚠️ 多层次标签仍需优化")
            return False
    else:
        logging.error("  ❌ 多层次标签逻辑失败")
        return False

if __name__ == "__main__":
    logging.info("开始测试修复后的首板策略标签逻辑...")
    success = test_shouban_labels()
    
    if success:
        logging.info("✅ 修复验证成功！可以继续运行完整程序")
    else:
        logging.error("❌ 修复验证失败，需要进一步调整")
