#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import logging
import numpy as np
import pandas as pd
import tensorflow as tf
import time
import uuid
import os
import threading
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_fixes.log'),
        logging.StreamHandler()
    ]
)

def test_dataset_creation_fix():
    """测试数据集创建冲突修复"""
    logging.info("🔧 测试数据集创建冲突修复...")
    
    def force_clear_tensorflow_state():
        tf.keras.backend.clear_session()
        if hasattr(tf.compat.v1, 'reset_default_graph'):
            tf.compat.v1.reset_default_graph()
        import gc
        gc.collect()
        time.sleep(0.1)
    
    def create_trial_dataset(X, y, batch_size, shuffle=True, dataset_type="train", trial_id=None):
        """修复后的数据集创建函数"""
        try:
            if trial_id is None:
                trial_id = f"{os.getpid()}_{threading.get_ident()}_{uuid.uuid4().hex[:8]}_{int(time.time() * 1000000)}"
            
            force_clear_tensorflow_state()
            
            dataset = tf.data.Dataset.from_tensor_slices((X, y))
            if shuffle:
                seed = abs(hash(f"{trial_id}_{dataset_type}_{time.time()}")) % 2**31
                dataset = dataset.shuffle(buffer_size=min(len(X), 1000), seed=seed)
            dataset = dataset.batch(batch_size)
            dataset = dataset.prefetch(tf.data.AUTOTUNE)
            
            logging.info(f"✅ 创建{dataset_type}数据集成功，Trial: {trial_id}")
            return dataset
            
        except Exception as e:
            logging.error(f"❌ Trial {trial_id} 数据集创建失败: {e}")
            raise e
    
    # 测试多次创建
    X = np.random.random((100, 10, 5))
    y = {'output1': np.random.randint(0, 2, 100), 'output2': np.random.random(100)}
    
    success_count = 0
    for i in range(5):
        try:
            dataset = create_trial_dataset(X, y, 32, shuffle=True, dataset_type=f"test_{i}")
            success_count += 1
        except Exception as e:
            logging.error(f"Trial {i} 失败: {e}")
    
    logging.info(f"数据集创建测试结果: {success_count}/5 成功")
    return success_count == 5

def test_auc_metric_matching():
    """测试AUC指标匹配修复"""
    logging.info("🔧 测试AUC指标匹配修复...")
    
    def _find_dynamic_auc_metric(available_metrics):
        """修复后的AUC指标匹配函数"""
        logging.info(f"🔍 开始动态匹配指标，可用指标: {available_metrics}")
        
        auc_patterns = [
            r'val_classification_output_1_auc$',
            r'val_classification_output_1_.*auc$',
            r'val_classification_output_1_auc.*',
            r'val_.*classification_output_1.*auc.*',
            r'val_auc.*',
            r'val_binary_accuracy.*'
        ]
        
        for i, pattern in enumerate(auc_patterns):
            matching_keys = [k for k in available_metrics if re.search(pattern, k)]
            if matching_keys:
                best_key = min(matching_keys, key=len)
                logging.info(f"✅ 动态匹配找到AUC指标: {best_key} (模式{i+1}: {pattern})")
                return best_key
        
        return None
    
    # 测试不同的指标名称
    test_cases = [
        ['val_classification_output_1_auc', 'val_loss'],
        ['val_classification_output_1_classification_output_1_auc', 'val_loss'],
        ['val_classification_output_2_classification_output_2_auc', 'val_loss'],
        ['val_auc', 'val_loss'],
        ['val_binary_accuracy', 'val_loss'],
        ['val_loss', 'val_accuracy']  # 没有AUC的情况
    ]
    
    success_count = 0
    for i, metrics in enumerate(test_cases):
        result = _find_dynamic_auc_metric(metrics)
        if result:
            logging.info(f"测试{i+1}: 找到指标 {result}")
            success_count += 1
        else:
            logging.warning(f"测试{i+1}: 未找到合适指标")
    
    logging.info(f"AUC指标匹配测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count >= len(test_cases) - 1  # 允许最后一个测试失败

def test_data_condition_relaxation():
    """测试数据筛选条件放宽"""
    logging.info("🔧 测试数据筛选条件放宽...")
    
    # 创建模拟数据
    np.random.seed(42)
    data = {
        'ts_code': ['000001.SZ'] * 100,
        'trade_date': pd.date_range('2025-01-01', periods=100),
        'limit_up': np.random.choice([True, False], 100, p=[0.1, 0.9]),
        'pct_chg': np.random.normal(2, 5, 100),
        'vol': np.random.randint(1000, 10000, 100),
        '连续涨停天数': np.random.randint(0, 6, 100),
        'turnover_rate': np.random.uniform(0.5, 10, 100)
    }
    df = pd.DataFrame(data)
    
    # 修复后的首板条件（放宽版本）
    def get_relaxed_shouban_condition(df_subset):
        volume_condition = (df_subset['vol'] > df_subset.groupby('ts_code')['vol'].shift(1).fillna(0) * 1.1)
        
        strict_condition = (
            (df_subset['limit_up'] == True) &
            (df_subset['连续涨停天数'] == 1)
        )
        
        relaxed_condition_1 = (
            (df_subset['pct_chg'] >= 7.0) &
            (df_subset['连续涨停天数'] <= 3) &
            volume_condition
        )
        
        relaxed_condition_2 = (
            (df_subset['limit_up'] == True) &
            (df_subset['连续涨停天数'] <= 5)
        )
        
        relaxed_condition_3 = (
            (df_subset['pct_chg'] >= 5.0) &
            (df_subset['pct_chg'] <= 9.9) &
            volume_condition &
            (df_subset['连续涨停天数'] == 0)
        )
        
        return strict_condition | relaxed_condition_1 | relaxed_condition_2 | relaxed_condition_3
    
    # 修复后的连板条件（放宽版本）
    def get_relaxed_lianban_condition(df_subset):
        volume_condition = (df_subset['vol'] > df_subset.groupby('ts_code')['vol'].shift(1).fillna(0) * 1.1)
        
        strict_condition = (df_subset['连续涨停天数'] >= 2)
        
        relaxed_condition_1 = (
            (df_subset['pct_chg'] >= 6.0) &
            (df_subset['连续涨停天数'] >= 1) &
            volume_condition
        )
        
        relaxed_condition_2 = (
            (df_subset['limit_up'] == True) &
            (df_subset['连续涨停天数'] >= 1)
        )
        
        relaxed_condition_3 = (
            (df_subset['pct_chg'] >= 8.0) &
            volume_condition
        )
        
        relaxed_condition_4 = (
            (df_subset['limit_up'] == True) &
            (df_subset['turnover_rate'] >= 1.0)
        )
        
        return strict_condition | relaxed_condition_1 | relaxed_condition_2 | relaxed_condition_3 | relaxed_condition_4
    
    # 测试筛选结果
    shouban_samples = df[get_relaxed_shouban_condition(df)]
    lianban_samples = df[get_relaxed_lianban_condition(df)]
    
    logging.info(f"首板样本数量: {len(shouban_samples)} (原数据: {len(df)})")
    logging.info(f"连板样本数量: {len(lianban_samples)} (原数据: {len(df)})")
    
    # 检查是否有足够的样本
    shouban_success = len(shouban_samples) > 0
    lianban_success = len(lianban_samples) > 0
    
    logging.info(f"数据筛选测试结果: 首板{'✅' if shouban_success else '❌'}, 连板{'✅' if lianban_success else '❌'}")
    return shouban_success and lianban_success

def main():
    """主测试函数"""
    logging.info("🚀 开始测试修复效果...")
    
    results = {
        "数据集创建修复": test_dataset_creation_fix(),
        "AUC指标匹配修复": test_auc_metric_matching(),
        "数据筛选条件放宽": test_data_condition_relaxation()
    }
    
    logging.info("\n" + "="*50)
    logging.info("📊 修复效果测试总结:")
    logging.info("="*50)
    
    success_count = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    logging.info(f"\n总测试数: {len(results)}")
    logging.info(f"通过测试: {success_count}")
    logging.info(f"失败测试: {len(results) - success_count}")
    logging.info(f"通过率: {success_count/len(results)*100:.1f}%")
    
    if success_count == len(results):
        logging.info("🎉 所有修复测试通过！")
        return True
    else:
        logging.warning("⚠️ 部分修复测试失败，需要进一步调整")
        return False

if __name__ == "__main__":
    main()
