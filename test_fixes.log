2025-08-06 05:57:56,351 - INFO - 🚀 开始测试修复效果...
2025-08-06 05:57:56,351 - INFO - 🔧 测试数据集创建冲突修复...
2025-08-06 05:57:57,210 - INFO - ✅ 创建test_0数据集成功，Trial: 15735_4449797632_849af291_1754431076356724
2025-08-06 05:57:57,788 - INFO - ✅ 创建test_1数据集成功，Trial: 15735_4449797632_8ae5cee4_1754431077210703
2025-08-06 05:57:58,424 - INFO - ✅ 创建test_2数据集成功，Trial: 15735_4449797632_af6be380_1754431077796718
2025-08-06 05:57:58,945 - INFO - ✅ 创建test_3数据集成功，Trial: 15735_4449797632_55c12d16_1754431078425377
2025-08-06 05:57:59,470 - INFO - ✅ 创建test_4数据集成功，Trial: 15735_4449797632_24bac24b_1754431078945967
2025-08-06 05:57:59,471 - INFO - 数据集创建测试结果: 5/5 成功
2025-08-06 05:57:59,471 - INFO - 🔧 测试AUC指标匹配修复...
2025-08-06 05:57:59,471 - INFO - 🔍 开始动态匹配指标，可用指标: ['val_classification_output_1_auc', 'val_loss']
2025-08-06 05:57:59,471 - INFO - ✅ 动态匹配找到AUC指标: val_classification_output_1_auc (模式1: val_classification_output_1_auc$)
2025-08-06 05:57:59,471 - INFO - 测试1: 找到指标 val_classification_output_1_auc
2025-08-06 05:57:59,472 - INFO - 🔍 开始动态匹配指标，可用指标: ['val_classification_output_1_classification_output_1_auc', 'val_loss']
2025-08-06 05:57:59,472 - INFO - ✅ 动态匹配找到AUC指标: val_classification_output_1_classification_output_1_auc (模式2: val_classification_output_1_.*auc$)
2025-08-06 05:57:59,472 - INFO - 测试2: 找到指标 val_classification_output_1_classification_output_1_auc
2025-08-06 05:57:59,472 - INFO - 🔍 开始动态匹配指标，可用指标: ['val_classification_output_2_classification_output_2_auc', 'val_loss']
2025-08-06 05:57:59,473 - WARNING - 测试3: 未找到合适指标
2025-08-06 05:57:59,473 - INFO - 🔍 开始动态匹配指标，可用指标: ['val_auc', 'val_loss']
2025-08-06 05:57:59,473 - INFO - ✅ 动态匹配找到AUC指标: val_auc (模式5: val_auc.*)
2025-08-06 05:57:59,473 - INFO - 测试4: 找到指标 val_auc
2025-08-06 05:57:59,473 - INFO - 🔍 开始动态匹配指标，可用指标: ['val_binary_accuracy', 'val_loss']
2025-08-06 05:57:59,473 - INFO - ✅ 动态匹配找到AUC指标: val_binary_accuracy (模式6: val_binary_accuracy.*)
2025-08-06 05:57:59,474 - INFO - 测试5: 找到指标 val_binary_accuracy
2025-08-06 05:57:59,474 - INFO - 🔍 开始动态匹配指标，可用指标: ['val_loss', 'val_accuracy']
2025-08-06 05:57:59,475 - WARNING - 测试6: 未找到合适指标
2025-08-06 05:57:59,475 - INFO - AUC指标匹配测试结果: 4/6 成功
2025-08-06 05:57:59,475 - INFO - 🔧 测试数据筛选条件放宽...
2025-08-06 05:57:59,527 - INFO - 首板样本数量: 17 (原数据: 100)
2025-08-06 05:57:59,527 - INFO - 连板样本数量: 72 (原数据: 100)
2025-08-06 05:57:59,528 - INFO - 数据筛选测试结果: 首板✅, 连板✅
2025-08-06 05:57:59,529 - INFO - 
==================================================
2025-08-06 05:57:59,529 - INFO - 📊 修复效果测试总结:
2025-08-06 05:57:59,529 - INFO - ==================================================
2025-08-06 05:57:59,530 - INFO - 数据集创建修复: ✅ 通过
2025-08-06 05:57:59,530 - INFO - AUC指标匹配修复: ❌ 失败
2025-08-06 05:57:59,530 - INFO - 数据筛选条件放宽: ✅ 通过
2025-08-06 05:57:59,530 - INFO - 
总测试数: 3
2025-08-06 05:57:59,530 - INFO - 通过测试: 2
2025-08-06 05:57:59,530 - INFO - 失败测试: 1
2025-08-06 05:57:59,531 - INFO - 通过率: 66.7%
2025-08-06 05:57:59,531 - WARNING - ⚠️ 部分修复测试失败，需要进一步调整
